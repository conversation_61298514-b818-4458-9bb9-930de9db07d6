const passport = require('passport');
const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { roleRights } = require('../config/roles');

const verifyCallback = (req, resolve, reject, requiredRights) => async (err, user, info) => {
  if (err || info || !user) {
    return reject(new ApiError(httpStatus.UNAUTHORIZED, 'Please authenticate'));
  }

  req.user = user;

  try {
    if (user.role === 'customer') {
      // Use customer data from JWT token instead of database
      req.customer = user.customerProfile || null;
    } else if (user.role === 'provider') {
      // Use provider data from JWT token instead of database
      const provider = user.providerProfile || {};

      // Only check approval status for non-profile routes
      if (!(req.path === '/profile' && (req.method === 'GET' || req.method === 'POST'))) {
        if (!provider.hasOwnProperty('isApproved')) {
          return reject(new ApiError(httpStatus.NOT_ACCEPTABLE, 'Please complete your profile'));
        } else if (provider.isApproved !== 'approved') {
          return reject(new ApiError(httpStatus.NOT_ACCEPTABLE, 'Please wait for admin approval, currently your status is, ' + provider.isApproved));
        }
      }
      req.provider = provider;
    }

    // Rights checking - no database needed
    if (requiredRights.length) {
      const userRights = roleRights.get(user.role);
      if (!userRights) {
        return reject(new ApiError(httpStatus.FORBIDDEN, 'Invalid user role'));
      }

      const hasRequiredRights = requiredRights.every((requiredRight) => userRights.includes(requiredRight));
      if (!hasRequiredRights && req.params.userId !== user.id) {
        return reject(new ApiError(httpStatus.FORBIDDEN, 'Forbidden'));
      }
    }

    resolve();
  } catch (error) {
    console.error('Auth middleware error:', error);
    return reject(new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Authentication error'));
  }
};

const auth =
  (...requiredRights) =>
  async (req, res, next) => {
    return new Promise((resolve, reject) => {
      passport.authenticate('jwt', { session: false }, verifyCallback(req, resolve, reject, requiredRights))(req, res, next);
    })
      .then(() => next())
      .catch((err) => next(err));
  };

module.exports = auth;
